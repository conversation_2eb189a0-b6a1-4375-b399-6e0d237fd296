{
  "name": "interview-next-service",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@livekit/components-react": "^2.9.14",
    "@livekit/components-styles": "^1.1.6",
    "livekit-client": "^2.13.3",
<<<<<<< HEAD
    "livekit-server-sdk": "^2.13.1",
    "next": "^15.4.4",
=======
    "next": "15.3.2",
>>>>>>> origin/main
    "react": "^19.0.0",
    "react-dom": "^19.0.0"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@tailwindcss/postcss": "^4",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "eslint": "^9",
    "eslint-config-next": "15.3.2",
    "tailwindcss": "^4",
    "typescript": "^5"
  }
}
