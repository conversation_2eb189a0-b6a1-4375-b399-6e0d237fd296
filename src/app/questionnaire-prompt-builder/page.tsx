"use client";

import { useState, useEffect } from "react";

export default function QuestionnairePromptBuilder() {
  const [prompt, setPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "success" | "error">(
    "idle"
  );

  // Load existing prompt on component mount
  useEffect(() => {
    const loadPrompt = async () => {
      try {
        const response = await fetch("/api/questionnaire-prompt-builder");
        if (response.ok) {
          const data = await response.json();
          setPrompt(data.prompt || "");
        }
      } catch (error) {
        console.error("Failed to load prompt:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPrompt();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus("idle");

    try {
      const response = await fetch("/api/questionnaire-prompt-builder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt }),
      });

      if (response.ok) {
        setSaveStatus("success");
        setTimeout(() => setSaveStatus("idle"), 3000);
      } else {
        setSaveStatus("error");
      }
    } catch (error) {
      console.error("Failed to save prompt:", error);
      setSaveStatus("error");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === "s") {
      e.preventDefault();
      if (prompt.trim() && !isSaving) {
        handleSave();
      }
    }
  };

  const handleReset = () => {
    setPrompt(
      `You are an AI interviewer conducting a professional interview. Your role is to:
- Ask thoughtful, relevant questions based on the candidate's responses
- Listen actively and ask follow-up questions when appropriate
- Maintain a friendly but professional tone throughout
- Keep the conversation natural and engaging
- Conclude the interview gracefully after covering key topics`
    );
    setSaveStatus("idle");
  };

  if (isLoading) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background flex items-center justify-center">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Questionnaire Prompt Builder
          </h1>
          <p className="text-foreground/70">
            Create and customize the AI interviewer prompt that will guide the
            interview conversation.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label
                htmlFor="prompt"
                className="block text-sm font-medium text-foreground"
              >
                Interview Prompt
              </label>
              <span className="text-sm text-foreground/60">
                {prompt.length} characters
              </span>
            </div>
            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter the prompt that will guide the AI interviewer...

Example:
You are an AI interviewer conducting a professional interview. Your role is to:
- Ask thoughtful, relevant questions based on the candidate's responses
- Listen actively and ask follow-up questions when appropriate
- Maintain a friendly but professional tone throughout
- Keep the conversation natural and engaging
- Conclude the interview gracefully after covering key topics"
              className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-lg
                         bg-white dark:bg-gray-700 text-foreground
                         focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                         resize-vertical min-h-[200px]"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex gap-3">
              <button
                onClick={handleSave}
                disabled={isSaving || !prompt.trim()}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400
                           text-white font-medium rounded-lg transition-colors
                           disabled:cursor-not-allowed"
                title="Save prompt (Ctrl/Cmd + S)"
              >
                {isSaving ? "Saving..." : "Save Prompt"}
              </button>

              <button
                onClick={handleReset}
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 
                           text-white font-medium rounded-lg transition-colors"
              >
                Reset to Default
              </button>
            </div>

            {saveStatus === "success" && (
              <div className="text-green-600 font-medium">
                ✓ Prompt saved successfully!
              </div>
            )}

            {saveStatus === "error" && (
              <div className="text-red-600 font-medium">
                ✗ Failed to save prompt. Please try again.
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h2 className="text-lg font-semibold text-foreground mb-3">
            💡 Prompt Writing Tips
          </h2>
          <ul className="space-y-2 text-foreground/80">
            <li>• Be specific about the interview style and tone you want</li>
            <li>
              • Include instructions for how to handle different types of
              responses
            </li>
            <li>• Specify the types of questions the AI should ask</li>
            <li>• Consider including guidelines for follow-up questions</li>
            <li>• Think about how the AI should conclude the interview</li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <a
            href="/call"
            className="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 
                       text-white font-medium rounded-lg transition-colors"
          >
            Start Interview Call →
          </a>
        </div>
      </div>
    </div>
  );
}
