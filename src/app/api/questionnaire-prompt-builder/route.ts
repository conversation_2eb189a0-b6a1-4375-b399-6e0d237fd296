import { NextResponse } from "next/server";

// In-memory storage for simplicity (you could use filesystem or database)
let storedPrompt =
  "You are an AI interviewer. Conduct a professional interview by asking thoughtful questions and listening to responses. Keep the conversation natural and engaging.";

export async function GET() {
  return NextResponse.json({ prompt: storedPrompt });
}

export async function POST(request: Request) {
  try {
    const { prompt } = await request.json();
    storedPrompt = prompt;
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }
}
