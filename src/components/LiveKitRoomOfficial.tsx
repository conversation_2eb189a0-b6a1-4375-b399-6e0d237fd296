"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  useParticipants,
} from "@livekit/components-react";
import "@livekit/components-styles";

interface LiveKitRoomOfficialProps {
  token: string;
  serverUrl: string;
  onDisconnect: () => void;
}

function RoomContent() {
  const participants = useParticipants();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-foreground mb-2">
            Interview in Progress
          </h2>
          <p className="text-foreground/70">
            {participants.length > 1
              ? `Connected with AI Interviewer (${participants.length} participants)`
              : "Waiting for AI Interviewer to join..."}
          </p>
        </div>

        <div className="space-y-4">
          {participants.map((participant) => (
            <div
              key={participant.identity}
              className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-3 h-3 rounded-full ${
                    participant.identity.includes("agent")
                      ? "bg-blue-500"
                      : "bg-green-500"
                  }`}
                ></div>
                <span className="font-medium text-foreground">
                  {participant.identity.includes("agent")
                    ? "AI Interviewer"
                    : participant.identity}
                </span>
                <span className="text-sm text-foreground/60">
                  {participant.isSpeaking ? "🎤 Speaking" : "🔇 Silent"}
                </span>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-foreground/60 mb-4">
            Make sure your microphone is enabled and speak clearly.
          </p>
        </div>
      </div>

      {/* This handles all audio rendering automatically */}
      <RoomAudioRenderer />
    </div>
  );
}

export default function LiveKitRoomOfficial({
  token,
  serverUrl,
  onDisconnect,
}: LiveKitRoomOfficialProps) {
  return (
    <LiveKitRoom
      token={token}
      serverUrl={serverUrl}
      connect={true}
      audio={true}
      video={false}
      onDisconnected={onDisconnect}
      onError={(error) => {
        console.error("LiveKit error:", error);
      }}
      style={{ height: "100%" }}
    >
      <RoomContent />
    </LiveKitRoom>
  );
}
