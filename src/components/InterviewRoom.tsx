"use client";

import LiveKitRoomOfficial from "@/components/LiveKitRoomOfficial";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

interface InterviewRoomProps {
  roomData: RoomData;
  onDisconnect: () => void;
}

export default function InterviewRoom({ roomData, onDisconnect }: InterviewRoomProps) {
  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <LiveKitRoomOfficial
        token={roomData.token}
        serverUrl={roomData.serverUrl}
        onDisconnect={onDisconnect}
      />
    </div>
  );
}
