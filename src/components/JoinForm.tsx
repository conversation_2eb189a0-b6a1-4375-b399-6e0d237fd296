"use client";

import { useState } from "react";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

interface JoinFormProps {
  onJoinSuccess: (roomData: RoomData) => void;
}

export default function JoinForm({ onJoinSuccess }: JoinFormProps) {
  const [participantName, setParticipantName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const joinInterview = async () => {
    if (!participantName.trim()) {
      setError("Please enter your name");
      return;
    }

    setIsCreating(true);
    setError(null);

    try {
      const response = await fetch("/api/join-interview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ participantName: participantName.trim() }),
      });

      if (!response.ok) {
        throw new Error("Failed to join interview");
      }

      const data = await response.json();
      onJoinSuccess(data);
    } catch (err) {
      console.error("Error joining interview:", err);
      setError("Failed to join interview. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center p-6">
        <h1 className="text-3xl font-bold text-foreground mb-6">
          Start Interview Call
        </h1>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
          <div className="mb-6">
            <label
              htmlFor="participantName"
              className="block text-sm font-medium text-foreground mb-2"
            >
              Your Name
            </label>
            <input
              id="participantName"
              type="text"
              value={participantName}
              onChange={(e) => setParticipantName(e.target.value)}
              placeholder="Enter your name for the interview"
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                         bg-white dark:bg-gray-700 text-foreground
                         focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              onKeyDown={(e) => e.key === "Enter" && joinInterview()}
            />
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-800 dark:text-red-200 text-sm">{error}</p>
            </div>
          )}

          <button
            onClick={joinInterview}
            disabled={isCreating || !participantName.trim()}
            className="w-full px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400
                       text-white font-medium rounded-lg transition-colors
                       disabled:cursor-not-allowed"
          >
            {isCreating ? "Joining Interview..." : "Join Interview"}
          </button>
        </div>

        <div className="space-y-4">
          <a
            href="/questionnaire-prompt-builder"
            className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700
                       text-white font-medium rounded-lg transition-colors"
          >
            ← Back to Prompt Builder
          </a>

          <div className="text-foreground/50">
            <p>Make sure to configure your interview prompt first.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
